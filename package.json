{"name": "react-temp", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "prod": "vite --mode production", "build:dev": "vite build --mode development", "build:prod": "vite build --mode production", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@reduxjs/toolkit": "^1.9.5", "@types/node": "^20.4.5", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "axios": "^0.27.2", "pdfjs-dist": "^5.4.54", "react": "^18.2.0", "react-dom": "^18.2.0", "react-pdf": "^10.0.1", "react-redux": "^8.1.1", "react-router-dom": "^6.14.2", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/react-window": "^1.8.8", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "less": "^4.1.3", "less-loader": "^11.1.3", "vite": "^4.4.5"}}