import React, { useState } from 'react';
import { 
  <PERSON>, 
  <PERSON>pography, 
  Button, 
  Alert, 
  Paper,
  Container,
  Stack,
  Card,
  CardContent,
  Divider
} from '@mui/material';
import { Upload, Description, Visibility } from '@mui/icons-material';
import PDFViewerSimple from '../../components/PDFViewer/PDFViewerSimple';
import './PDFDemo.less';

const PDFDemoSimple: React.FC = () => {
  const [pdfSrc, setPdfSrc] = useState<string | File | null>(null);
  const [fileName, setFileName] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [numPages, setNumPages] = useState<number>(0);

  // 处理文件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.type !== 'application/pdf') {
      setError('请选择PDF文件');
      return;
    }

    setError(null);
    setFileName(file.name);
    setPdfSrc(file);
  };

  // 加载示例PDF
  const loadSamplePDF = () => {
    // 使用一个公开的示例PDF文件
    const samplePdfUrl = 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf';
    setPdfSrc(samplePdfUrl);
    setFileName('示例PDF文档');
    setError(null);
  };

  // PDF加载成功回调
  const handleLoadSuccess = (pages: number) => {
    setNumPages(pages);
    setError(null);
  };

  // PDF加载错误回调
  const handleLoadError = (err: Error) => {
    setError(`PDF加载失败: ${err.message}`);
  };

  return (
    <Container maxWidth="xl" className="pdf-demo-container">
      <Box sx={{ py: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom align="center">
          PDF查看器演示
        </Typography>
        <Typography variant="h6" color="text.secondary" align="center" sx={{ mb: 4 }}>
          支持虚拟滚动、缩放控制和响应式设计的PDF预览组件
        </Typography>

        <Box sx={{ display: 'flex', gap: 4, flexDirection: { xs: 'column', md: 'row' } }}>
          {/* 左侧控制面板 */}
          <Box sx={{ width: { xs: '100%', md: '300px' }, flexShrink: 0 }}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  <Upload sx={{ mr: 1, verticalAlign: 'middle' }} />
                  文件上传
                </Typography>
                
                <Stack spacing={2}>
                  <Box>
                    <input
                      accept="application/pdf"
                      style={{ display: 'none' }}
                      id="pdf-upload"
                      type="file"
                      onChange={handleFileUpload}
                    />
                    <label htmlFor="pdf-upload">
                      <Button
                        variant="contained"
                        component="span"
                        fullWidth
                        startIcon={<Upload />}
                      >
                        选择PDF文件
                      </Button>
                    </label>
                  </Box>

                  <Button
                    variant="outlined"
                    fullWidth
                    startIcon={<Description />}
                    onClick={loadSamplePDF}
                  >
                    加载示例PDF
                  </Button>
                </Stack>

                <Divider sx={{ my: 2 }} />

                <Typography variant="h6" gutterBottom>
                  <Visibility sx={{ mr: 1, verticalAlign: 'middle' }} />
                  文档信息
                </Typography>

                {fileName && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      文件名:
                    </Typography>
                    <Typography variant="body1" sx={{ wordBreak: 'break-all' }}>
                      {fileName}
                    </Typography>
                  </Box>
                )}

                {numPages > 0 && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      总页数:
                    </Typography>
                    <Typography variant="body1">
                      {numPages} 页
                    </Typography>
                  </Box>
                )}

                <Divider sx={{ my: 2 }} />

                <Typography variant="h6" gutterBottom>
                  功能特性
                </Typography>
                <Box component="ul" sx={{ pl: 2, m: 0 }}>
                  <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                    虚拟滚动支持大文档
                  </Typography>
                  <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                    缩放控制 (50% - 300%)
                  </Typography>
                  <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                    页面导航控制
                  </Typography>
                  <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                    响应式设计
                  </Typography>
                  <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                    浮动控制栏
                  </Typography>
                  <Typography component="li" variant="body2">
                    从左上角缩放
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Box>

          {/* 右侧PDF查看器 */}
          <Box sx={{ flex: 1, minWidth: 0 }}>
            <Paper 
              elevation={3} 
              sx={{ 
                height: '80vh', 
                minHeight: 600,
                overflow: 'hidden',
                borderRadius: 2
              }}
            >
              {error && (
                <Box sx={{ p: 2 }}>
                  <Alert severity="error">{error}</Alert>
                </Box>
              )}
              
              {!pdfSrc && !error && (
                <Box 
                  sx={{ 
                    height: '100%', 
                    display: 'flex', 
                    flexDirection: 'column',
                    alignItems: 'center', 
                    justifyContent: 'center',
                    color: 'text.secondary'
                  }}
                >
                  <Description sx={{ fontSize: 64, mb: 2, opacity: 0.5 }} />
                  <Typography variant="h6" gutterBottom>
                    请选择一个PDF文件
                  </Typography>
                  <Typography variant="body2">
                    支持拖拽上传或点击选择文件
                  </Typography>
                </Box>
              )}

              {pdfSrc && !error && (
                <PDFViewerSimple
                  src={pdfSrc}
                  width="100%"
                  height="100%"
                  initialZoom={1}
                  minZoom={0.5}
                  maxZoom={3}
                  zoomStep={0.25}
                  pageGap={10}
                  onLoadSuccess={handleLoadSuccess}
                  onError={handleLoadError}
                />
              )}
            </Paper>
          </Box>
        </Box>

        {/* 使用说明 */}
        <Box sx={{ mt: 4 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                使用说明
              </Typography>
              <Box sx={{ display: 'flex', gap: 4, flexDirection: { xs: 'column', md: 'row' } }}>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    导航控制:
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    • 使用底部控制栏的导航按钮切换页面<br/>
                    • 支持首页、上一页、下一页、末页快速跳转<br/>
                    • 滚动鼠标滚轮浏览文档
                  </Typography>
                </Box>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    缩放控制:
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    • 使用 + / - 按钮进行缩放<br/>
                    • 缩放范围: 50% - 300%<br/>
                    • 缩放锚点固定在视口左上角
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Box>
      </Box>
    </Container>
  );
};

export default PDFDemoSimple;
