import React, { useState, useCallback, useMemo } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import { FixedSizeList as List } from 'react-window';
import { 
  ZoomIn, 
  ZoomOut, 
  NavigateBefore, 
  NavigateNext,
  FirstPage,
  LastPage 
} from '@mui/icons-material';
import { IconButton, Typography, Box, CircularProgress, Alert } from '@mui/material';
import './PDFViewer.less';

// 设置PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

export interface PDFViewerProps {
  /** PDF文件的URL或File对象 */
  src: string | File;
  /** 组件宽度，默认为100% */
  width?: string | number;
  /** 组件高度，默认为100% */
  height?: string | number;
  /** 初始缩放级别，默认为1 */
  initialZoom?: number;
  /** 最小缩放级别，默认为0.5 */
  minZoom?: number;
  /** 最大缩放级别，默认为3 */
  maxZoom?: number;
  /** 缩放步长，默认为0.25 */
  zoomStep?: number;
  /** 页面间距，默认为10px */
  pageGap?: number;
  /** 错误回调 */
  onError?: (error: Error) => void;
  /** 加载完成回调 */
  onLoadSuccess?: (numPages: number) => void;
}

const PDFViewerSimple: React.FC<PDFViewerProps> = ({
  src,
  width = '100%',
  height = '100%',
  initialZoom = 1,
  minZoom = 0.5,
  maxZoom = 3,
  zoomStep = 0.25,
  pageGap = 10,
  onError,
  onLoadSuccess
}) => {
  const [numPages, setNumPages] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [zoom, setZoom] = useState<number>(initialZoom);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [containerSize, setContainerSize] = useState({ width: 800, height: 600 });

  // 文档加载成功回调
  const onDocumentLoadSuccess = useCallback(({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
    setLoading(false);
    setError(null);
    onLoadSuccess?.(numPages);
  }, [onLoadSuccess]);

  // 文档加载错误回调
  const onDocumentLoadError = useCallback((error: Error) => {
    setLoading(false);
    setError(error.message);
    onError?.(error);
  }, [onError]);

  // 计算页面尺寸
  const pageWidth = useMemo(() => {
    return Math.min(containerSize.width - 40, 800) * zoom;
  }, [containerSize.width, zoom]);

  // 页面项渲染组件
  const PageItem: React.FC<{ index: number; style: React.CSSProperties }> = ({ index, style }) => {
    const pageNumber = index + 1;

    return (
      <div style={style} className="pdf-page-container">
        <div 
          className="pdf-page"
          style={{
            width: pageWidth,
            margin: '0 auto',
            marginBottom: pageGap,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            border: '1px solid #ddd',
            backgroundColor: '#fff',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            borderRadius: '4px'
          }}
        >
          <Page
            pageNumber={pageNumber}
            width={pageWidth}
            loading={<CircularProgress />}
            error={<Typography color="error">页面加载失败</Typography>}
            renderTextLayer={false}
            renderAnnotationLayer={false}
          />
        </div>
      </div>
    );
  };

  // 缩放控制
  const handleZoomIn = useCallback(() => {
    setZoom(prev => Math.min(prev + zoomStep, maxZoom));
  }, [zoomStep, maxZoom]);

  const handleZoomOut = useCallback(() => {
    setZoom(prev => Math.max(prev - zoomStep, minZoom));
  }, [zoomStep, minZoom]);

  // 页面导航
  const goToPage = useCallback((pageNumber: number) => {
    if (pageNumber >= 1 && pageNumber <= numPages) {
      setCurrentPage(pageNumber);
    }
  }, [numPages]);

  const goToPrevPage = useCallback(() => {
    goToPage(Math.max(currentPage - 1, 1));
  }, [currentPage, goToPage]);

  const goToNextPage = useCallback(() => {
    goToPage(Math.min(currentPage + 1, numPages));
  }, [currentPage, numPages, goToPage]);

  const goToFirstPage = useCallback(() => {
    goToPage(1);
  }, [goToPage]);

  const goToLastPage = useCallback(() => {
    goToPage(numPages);
  }, [numPages, goToPage]);

  // 监听滚动更新当前页面
  const handleItemsRendered = useCallback(({ visibleStartIndex }: { visibleStartIndex: number }) => {
    setCurrentPage(visibleStartIndex + 1);
  }, []);

  if (loading) {
    return (
      <Box 
        display="flex" 
        justifyContent="center" 
        alignItems="center" 
        width={width} 
        height={height}
      >
        <CircularProgress />
        <Typography variant="body1" sx={{ ml: 2 }}>加载PDF中...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box width={width} height={height}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <div 
      className="pdf-viewer-container"
      style={{ 
        width, 
        height, 
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      <Document
        file={src}
        onLoadSuccess={onDocumentLoadSuccess}
        onLoadError={onDocumentLoadError}
        loading={<CircularProgress />}
        error={<Alert severity="error">PDF加载失败</Alert>}
      >
        {numPages > 0 && (
          <>
            <List
              width={containerSize.width}
              height={typeof height === 'string' ? 600 : height}
              itemCount={numPages}
              itemSize={Math.ceil(pageWidth * 1.414) + pageGap} // A4比例
              onItemsRendered={handleItemsRendered}
              className="pdf-viewer-list"
            >
              {PageItem}
            </List>

            {/* 浮动控制栏 */}
            <Box className="pdf-control-bar">
              <IconButton onClick={goToFirstPage} disabled={currentPage === 1}>
                <FirstPage />
              </IconButton>
              <IconButton onClick={goToPrevPage} disabled={currentPage === 1}>
                <NavigateBefore />
              </IconButton>
              
              <Typography variant="body2" sx={{ mx: 2 }}>
                {currentPage} / {numPages}
              </Typography>
              
              <IconButton onClick={goToNextPage} disabled={currentPage === numPages}>
                <NavigateNext />
              </IconButton>
              <IconButton onClick={goToLastPage} disabled={currentPage === numPages}>
                <LastPage />
              </IconButton>
              
              <Box sx={{ mx: 2, borderLeft: '1px solid #ddd', height: 24 }} />
              
              <IconButton onClick={handleZoomOut} disabled={zoom <= minZoom}>
                <ZoomOut />
              </IconButton>
              <Typography variant="body2" sx={{ mx: 1, minWidth: 50, textAlign: 'center' }}>
                {Math.round(zoom * 100)}%
              </Typography>
              <IconButton onClick={handleZoomIn} disabled={zoom >= maxZoom}>
                <ZoomIn />
              </IconButton>
            </Box>
          </>
        )}
      </Document>
    </div>
  );
};

export default PDFViewerSimple;
