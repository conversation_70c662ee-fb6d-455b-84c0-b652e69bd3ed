.pdf-viewer-container {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  overflow: hidden;

  .pdf-viewer-list {
    width: 100%;
    height: 100%;
    
    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 8px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;
      
      &:hover {
        background: #a8a8a8;
      }
    }
  }

  .pdf-page-container {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding: 10px;
  }

  .pdf-page {
    position: relative;
    border-radius: 4px;
    transition: box-shadow 0.2s ease;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    canvas {
      border-radius: 4px;
      max-width: 100%;
      height: auto;
      display: block;
    }
  }

  // 浮动控制栏
  .pdf-control-bar {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 24px;
    padding: 8px 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    z-index: 1000;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.98);
      box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
    }

    .MuiIconButton-root {
      padding: 6px;
      margin: 0 2px;
      transition: all 0.2s ease;
      
      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
        transform: scale(1.05);
      }
      
      &:disabled {
        opacity: 0.5;
        transform: none;
      }

      .MuiSvgIcon-root {
        font-size: 20px;
      }
    }

    .MuiTypography-root {
      font-weight: 500;
      color: #333;
      user-select: none;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .pdf-viewer-container {
    .pdf-control-bar {
      bottom: 10px;
      padding: 6px 12px;
      border-radius: 20px;

      .MuiIconButton-root {
        padding: 4px;
        
        .MuiSvgIcon-root {
          font-size: 18px;
        }
      }

      .MuiTypography-root {
        font-size: 0.875rem;
        margin: 0 8px !important;
      }
    }

    .pdf-page-container {
      padding: 5px;
    }
  }
}

@media (max-width: 480px) {
  .pdf-viewer-container {
    .pdf-control-bar {
      padding: 4px 8px;
      
      .MuiTypography-root {
        font-size: 0.75rem;
        margin: 0 4px !important;
      }
    }
  }
}

// 加载状态样式
.pdf-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #666;

  .MuiCircularProgress-root {
    margin-bottom: 16px;
  }
}

// 错误状态样式
.pdf-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 20px;

  .MuiAlert-root {
    max-width: 400px;
  }
}

// 页面加载动画
@keyframes pageLoad {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.pdf-page {
  animation: pageLoad 0.3s ease-out;
}

// 缩放过渡效果
.pdf-page canvas {
  transition: transform 0.2s ease;
}

// 控制栏按钮悬停效果
.pdf-control-bar .MuiIconButton-root {
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
  }

  &:hover::before {
    width: 40px;
    height: 40px;
  }
}

// 深色主题支持
@media (prefers-color-scheme: dark) {
  .pdf-viewer-container {
    background-color: #1a1a1a;

    .pdf-control-bar {
      background: rgba(30, 30, 30, 0.95);
      border: 1px solid rgba(255, 255, 255, 0.1);

      &:hover {
        background: rgba(30, 30, 30, 0.98);
      }

      .MuiTypography-root {
        color: #fff;
      }

      .MuiIconButton-root {
        color: #fff;

        &:hover {
          background-color: rgba(255, 255, 255, 0.08);
        }
      }
    }

    .pdf-viewer-list {
      &::-webkit-scrollbar-track {
        background: #2a2a2a;
      }
      
      &::-webkit-scrollbar-thumb {
        background: #555;
        
        &:hover {
          background: #777;
        }
      }
    }
  }
}
