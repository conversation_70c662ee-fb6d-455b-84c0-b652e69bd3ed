import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { FixedSizeList as List } from 'react-window';
import * as pdfjsLib from 'pdfjs-dist';
import { ZoomIn, ZoomOut, NavigateBefore, NavigateNext, FirstPage, LastPage } from '@mui/icons-material';
import { IconButton, Typography, Box, CircularProgress, Alert } from '@mui/material';
import './PDFViewer.less';

// 设置PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = new URL('pdfjs-dist/build/pdf.worker.min.js', import.meta.url).toString();

export interface PDFViewerProps {
  /** PDF文件的URL或ArrayBuffer */
  src: string | ArrayBuffer;
  /** 组件宽度，默认为100% */
  width?: string | number;
  /** 组件高度，默认为100% */
  height?: string | number;
  /** 初始缩放级别，默认为1 */
  initialZoom?: number;
  /** 最小缩放级别，默认为0.5 */
  minZoom?: number;
  /** 最大缩放级别，默认为3 */
  maxZoom?: number;
  /** 缩放步长，默认为0.25 */
  zoomStep?: number;
  /** 页面间距，默认为10px */
  pageGap?: number;
  /** 错误回调 */
  onError?: (error: Error) => void;
  /** 加载完成回调 */
  onLoadSuccess?: (numPages: number) => void;
}

interface PageData {
  pageNumber: number;
  canvas?: HTMLCanvasElement;
  isLoaded: boolean;
  isLoading: boolean;
}

const PDFViewer: React.FC<PDFViewerProps> = ({
  src,
  width = '100%',
  height = '100%',
  initialZoom = 1,
  minZoom = 0.5,
  maxZoom = 3,
  zoomStep = 0.25,
  pageGap = 10,
  onError,
  onLoadSuccess,
}) => {
  const [pdfDocument, setPdfDocument] = useState<pdfjsLib.PDFDocumentProxy | null>(null);
  const [numPages, setNumPages] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [zoom, setZoom] = useState<number>(initialZoom);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [pages, setPages] = useState<PageData[]>([]);
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });

  const containerRef = useRef<HTMLDivElement>(null);
  const listRef = useRef<List>(null);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);

  // 监听容器尺寸变化
  useEffect(() => {
    if (!containerRef.current) return;

    const updateSize = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setContainerSize({ width: rect.width, height: rect.height });
      }
    };

    updateSize();

    resizeObserverRef.current = new ResizeObserver(updateSize);
    resizeObserverRef.current.observe(containerRef.current);

    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }
    };
  }, []);

  // 加载PDF文档
  useEffect(() => {
    const loadPDF = async () => {
      try {
        setLoading(true);
        setError(null);

        const loadingTask = pdfjsLib.getDocument(src);
        const pdf = await loadingTask.promise;

        setPdfDocument(pdf);
        setNumPages(pdf.numPages);

        // 初始化页面数据
        const initialPages: PageData[] = Array.from({ length: pdf.numPages }, (_, index) => ({
          pageNumber: index + 1,
          isLoaded: false,
          isLoading: false,
        }));
        setPages(initialPages);

        onLoadSuccess?.(pdf.numPages);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : '加载PDF失败';
        setError(errorMessage);
        onError?.(err instanceof Error ? err : new Error(errorMessage));
      } finally {
        setLoading(false);
      }
    };

    if (src) {
      loadPDF();
    }
  }, [src, onError, onLoadSuccess]);

  // 渲染PDF页面到Canvas
  const renderPage = useCallback(
    async (pageNumber: number): Promise<HTMLCanvasElement | null> => {
      if (!pdfDocument) return null;

      try {
        const page = await pdfDocument.getPage(pageNumber);
        const viewport = page.getViewport({ scale: zoom });

        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');

        if (!context) return null;

        canvas.height = viewport.height;
        canvas.width = viewport.width;

        const renderContext = {
          canvasContext: context,
          viewport: viewport,
        };

        await page.render(renderContext).promise;
        return canvas;
      } catch (err) {
        console.error(`渲染页面 ${pageNumber} 失败:`, err);
        return null;
      }
    },
    [pdfDocument, zoom]
  );

  // 计算页面尺寸
  const pageSize = useMemo(() => {
    if (!pdfDocument || !containerSize.width) return { width: 0, height: 0 };

    // 假设所有页面尺寸相同，使用第一页计算
    const baseWidth = containerSize.width - 40; // 留出边距
    const baseHeight = baseWidth * 1.414; // A4比例

    return {
      width: baseWidth * zoom,
      height: baseHeight * zoom,
    };
  }, [containerSize.width, zoom, pdfDocument]);

  // 页面项渲染组件
  const PageItem: React.FC<{ index: number; style: React.CSSProperties }> = ({ index, style }) => {
    const pageNumber = index + 1;
    const [pageCanvas, setPageCanvas] = useState<HTMLCanvasElement | null>(null);
    const [isPageLoading, setIsPageLoading] = useState(false);

    useEffect(() => {
      const loadPage = async () => {
        if (pages[index]?.isLoaded && pages[index]?.canvas) {
          setPageCanvas(pages[index].canvas!);
          return;
        }

        setIsPageLoading(true);
        const canvas = await renderPage(pageNumber);

        if (canvas) {
          setPageCanvas(canvas);
          setPages((prev) => {
            const newPages = [...prev];
            newPages[index] = {
              ...newPages[index],
              canvas,
              isLoaded: true,
              isLoading: false,
            };
            return newPages;
          });
        }
        setIsPageLoading(false);
      };

      loadPage();
    }, [index, pageNumber]);

    return (
      <div style={style} className="pdf-page-container">
        <div
          className="pdf-page"
          style={{
            width: pageSize.width,
            height: pageSize.height,
            margin: '0 auto',
            marginBottom: pageGap,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            border: '1px solid #ddd',
            backgroundColor: '#fff',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          }}
        >
          {isPageLoading ? (
            <CircularProgress />
          ) : pageCanvas ? (
            <canvas
              ref={(canvasRef) => {
                if (canvasRef && pageCanvas) {
                  const ctx = canvasRef.getContext('2d');
                  if (ctx) {
                    canvasRef.width = pageCanvas.width;
                    canvasRef.height = pageCanvas.height;
                    ctx.drawImage(pageCanvas, 0, 0);
                  }
                }
              }}
              style={{
                maxWidth: '100%',
                maxHeight: '100%',
                display: 'block',
              }}
            />
          ) : (
            <Typography color="error">页面加载失败</Typography>
          )}
        </div>
      </div>
    );
  };

  // 缩放控制
  const handleZoomIn = useCallback(() => {
    setZoom((prev) => Math.min(prev + zoomStep, maxZoom));
  }, [zoomStep, maxZoom]);

  const handleZoomOut = useCallback(() => {
    setZoom((prev) => Math.max(prev - zoomStep, minZoom));
  }, [zoomStep, minZoom]);

  // 页面导航
  const goToPage = useCallback(
    (pageNumber: number) => {
      if (pageNumber >= 1 && pageNumber <= numPages && listRef.current) {
        setCurrentPage(pageNumber);
        listRef.current.scrollToItem(pageNumber - 1, 'start');
      }
    },
    [numPages]
  );

  const goToPrevPage = useCallback(() => {
    goToPage(Math.max(currentPage - 1, 1));
  }, [currentPage, goToPage]);

  const goToNextPage = useCallback(() => {
    goToPage(Math.min(currentPage + 1, numPages));
  }, [currentPage, numPages, goToPage]);

  const goToFirstPage = useCallback(() => {
    goToPage(1);
  }, [goToPage]);

  const goToLastPage = useCallback(() => {
    goToPage(numPages);
  }, [numPages, goToPage]);

  // 监听滚动更新当前页面
  const handleItemsRendered = useCallback(({ visibleStartIndex }: { visibleStartIndex: number }) => {
    setCurrentPage(visibleStartIndex + 1);
  }, []);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" width={width} height={height}>
        <CircularProgress />
        <Typography variant="body1" sx={{ ml: 2 }}>
          加载PDF中...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box width={width} height={height}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <div
      ref={containerRef}
      className="pdf-viewer-container"
      style={{
        width,
        height,
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {numPages > 0 && (
        <>
          <List
            ref={listRef}
            width={containerSize.width}
            height={containerSize.height}
            itemCount={numPages}
            itemSize={pageSize.height + pageGap}
            onItemsRendered={handleItemsRendered}
            className="pdf-viewer-list"
          >
            {PageItem}
          </List>

          {/* 浮动控制栏 */}
          <Box className="pdf-control-bar">
            <IconButton onClick={goToFirstPage} disabled={currentPage === 1}>
              <FirstPage />
            </IconButton>
            <IconButton onClick={goToPrevPage} disabled={currentPage === 1}>
              <NavigateBefore />
            </IconButton>

            <Typography variant="body2" sx={{ mx: 2 }}>
              {currentPage} / {numPages}
            </Typography>

            <IconButton onClick={goToNextPage} disabled={currentPage === numPages}>
              <NavigateNext />
            </IconButton>
            <IconButton onClick={goToLastPage} disabled={currentPage === numPages}>
              <LastPage />
            </IconButton>

            <Box sx={{ mx: 2, borderLeft: '1px solid #ddd', height: 24 }} />

            <IconButton onClick={handleZoomOut} disabled={zoom <= minZoom}>
              <ZoomOut />
            </IconButton>
            <Typography variant="body2" sx={{ mx: 1, minWidth: 50, textAlign: 'center' }}>
              {Math.round(zoom * 100)}%
            </Typography>
            <IconButton onClick={handleZoomIn} disabled={zoom >= maxZoom}>
              <ZoomIn />
            </IconButton>
          </Box>
        </>
      )}
    </div>
  );
};

export default PDFViewer;

