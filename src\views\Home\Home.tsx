import React from 'react';
import { Link } from 'react-router-dom';
import { Container, Typography, Card, CardContent, CardActions, Button, Grid, Box } from '@mui/material';
import { PictureAsPdf, Visibility, Code } from '@mui/icons-material';

const Home: React.FC = () => {
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box textAlign="center" mb={6}>
        <Typography variant="h2" component="h1" gutterBottom>
          React Vite 模板
        </Typography>
        <Typography variant="h5" color="text.secondary" gutterBottom>
          现代化的React开发环境
        </Typography>
      </Box>

      <Grid container spacing={4}>
        <Grid item xs={12} md={6} lg={4}>
          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <CardContent sx={{ flexGrow: 1 }}>
              <Box display="flex" alignItems="center" mb={2}>
                <PictureAsPdf color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6" component="h2">
                  PDF查看器
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                基于PDF.js的高性能PDF预览组件，支持虚拟滚动、缩放控制和响应式设计。
              </Typography>
              <Box component="ul" sx={{ mt: 2, pl: 2 }}>
                <Typography component="li" variant="body2">
                  虚拟滚动支持大文档
                </Typography>
                <Typography component="li" variant="body2">
                  浮动控制栏
                </Typography>
                <Typography component="li" variant="body2">
                  缩放和导航功能
                </Typography>
                <Typography component="li" variant="body2">
                  响应式设计
                </Typography>
              </Box>
            </CardContent>
            <CardActions>
              <Button component={Link} to="/pdf-demo" variant="contained" startIcon={<Visibility />} fullWidth>
                查看演示
              </Button>
            </CardActions>
          </Card>
        </Grid>

        <Grid item xs={12} md={6} lg={4}>
          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <CardContent sx={{ flexGrow: 1 }}>
              <Box display="flex" alignItems="center" mb={2}>
                <Code color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6" component="h2">
                  技术栈
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                本项目使用的主要技术和库：
              </Typography>
              <Box component="ul" sx={{ mt: 2, pl: 2 }}>
                <Typography component="li" variant="body2">
                  React 18 + TypeScript
                </Typography>
                <Typography component="li" variant="body2">
                  Vite 构建工具
                </Typography>
                <Typography component="li" variant="body2">
                  Material-UI 组件库
                </Typography>
                <Typography component="li" variant="body2">
                  PDF.js + React Window
                </Typography>
                <Typography component="li" variant="body2">
                  Less 样式预处理器
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6} lg={4}>
          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <CardContent sx={{ flexGrow: 1 }}>
              <Typography variant="h6" component="h2" gutterBottom>
                开始使用
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                快速开始开发：
              </Typography>
              <Box
                component="pre"
                sx={{
                  backgroundColor: '#f5f5f5',
                  p: 2,
                  borderRadius: 1,
                  fontSize: '0.875rem',
                  overflow: 'auto',
                }}
              >
                {`npm install
npm run dev`}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
};

export default Home;

