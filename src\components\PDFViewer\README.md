# PDF查看器组件

基于PDF.js和React Window的高性能PDF预览组件，支持虚拟滚动、缩放控制和响应式设计。

## 功能特性

- ✅ **虚拟滚动**: 使用react-window实现虚拟滚动，支持大型PDF文档的高效渲染
- ✅ **缩放控制**: 支持50%-300%的缩放范围，缩放锚点固定在视口左上角
- ✅ **浮动控制栏**: 底部居中的浮动控制栏，包含页面导航和缩放控制
- ✅ **响应式设计**: 自动适应父容器的宽度和高度
- ✅ **页面导航**: 支持首页、上一页、下一页、末页快速跳转
- ✅ **错误处理**: 完善的错误处理和加载状态显示
- ✅ **TypeScript支持**: 完整的TypeScript类型定义

## 安装依赖

```bash
npm install react-pdf react-window pdfjs-dist
npm install --save-dev @types/react-window
```

## 基本使用

```tsx
import React from 'react';
import PDFViewerSimple from './components/PDFViewer/PDFViewerSimple';

const App: React.FC = () => {
  const [pdfFile, setPdfFile] = useState<File | string | null>(null);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type === 'application/pdf') {
      setPdfFile(file);
    }
  };

  return (
    <div style={{ width: '100vw', height: '100vh' }}>
      <input type="file" accept=".pdf" onChange={handleFileUpload} />
      
      {pdfFile && (
        <PDFViewerSimple
          src={pdfFile}
          width="100%"
          height="80vh"
          initialZoom={1}
          onLoadSuccess={(numPages) => console.log(`加载成功，共${numPages}页`)}
          onError={(error) => console.error('PDF加载失败:', error)}
        />
      )}
    </div>
  );
};
```

## Props API

### PDFViewerSimple

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `src` | `string \| File` | - | PDF文件的URL或File对象 |
| `width` | `string \| number` | `'100%'` | 组件宽度 |
| `height` | `string \| number` | `'100%'` | 组件高度 |
| `initialZoom` | `number` | `1` | 初始缩放级别 |
| `minZoom` | `number` | `0.5` | 最小缩放级别 |
| `maxZoom` | `number` | `3` | 最大缩放级别 |
| `zoomStep` | `number` | `0.25` | 缩放步长 |
| `pageGap` | `number` | `10` | 页面间距(px) |
| `onLoadSuccess` | `(numPages: number) => void` | - | 加载成功回调 |
| `onError` | `(error: Error) => void` | - | 错误回调 |

## 高级用法

### 自定义样式

组件使用CSS类名进行样式控制，你可以通过覆盖以下类名来自定义样式：

```less
.pdf-viewer-container {
  // 主容器样式
}

.pdf-viewer-list {
  // 虚拟滚动列表样式
}

.pdf-page-container {
  // 页面容器样式
}

.pdf-page {
  // 单个页面样式
}

.pdf-control-bar {
  // 浮动控制栏样式
}
```

### 处理大文档

对于大型PDF文档，组件会自动使用虚拟滚动来优化性能：

```tsx
<PDFViewerSimple
  src={largePdfFile}
  width="100%"
  height="100vh"
  initialZoom={0.8} // 较小的初始缩放以显示更多内容
  pageGap={5} // 较小的页面间距以节省空间
/>
```

### 响应式设计

组件会自动适应容器大小，在移动设备上也能良好工作：

```tsx
<div style={{ 
  width: '100%', 
  height: '100vh',
  padding: '10px',
  boxSizing: 'border-box'
}}>
  <PDFViewerSimple
    src={pdfFile}
    width="100%"
    height="100%"
  />
</div>
```

## 技术实现

### 核心技术栈

- **PDF.js**: Mozilla开发的PDF渲染引擎
- **react-pdf**: PDF.js的React封装
- **react-window**: 虚拟滚动实现
- **Material-UI**: UI组件库

### 性能优化

1. **虚拟滚动**: 只渲染可见区域的页面，大幅减少DOM节点数量
2. **懒加载**: 页面按需加载和渲染
3. **缓存机制**: 已渲染的页面会被缓存，避免重复渲染
4. **响应式图片**: 根据缩放级别动态调整页面渲染质量

### 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 故障排除

### 常见问题

1. **PDF加载失败**
   - 检查PDF文件是否损坏
   - 确认文件URL是否可访问
   - 检查CORS设置（对于跨域PDF文件）

2. **性能问题**
   - 减小初始缩放级别
   - 增加页面间距以减少同时渲染的页面数
   - 检查PDF文件大小，考虑压缩

3. **样式问题**
   - 确保容器有明确的宽高设置
   - 检查CSS样式是否被其他样式覆盖
   - 使用浏览器开发者工具检查元素结构

### 调试模式

开启控制台可以看到详细的加载和渲染日志：

```tsx
<PDFViewerSimple
  src={pdfFile}
  onLoadSuccess={(numPages) => {
    console.log(`PDF加载成功，共${numPages}页`);
  }}
  onError={(error) => {
    console.error('PDF加载失败:', error);
  }}
/>
```

## 许可证

MIT License
