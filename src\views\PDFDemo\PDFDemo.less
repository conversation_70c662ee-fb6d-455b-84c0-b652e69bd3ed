.pdf-demo-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

  .MuiContainer-root {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .MuiCard-root {
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;

    &:hover {
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }
  }

  .MuiPaper-root {
    border-radius: 12px;
    overflow: hidden;
  }

  // 文件上传按钮样式
  .MuiButton-contained {
    background: linear-gradient(45deg, #2196F3 30%, #21CBF3 90%);
    border: 0;
    border-radius: 8px;
    box-shadow: 0 3px 5px 2px rgba(33, 203, 243, .3);
    color: white;
    height: 48px;
    padding: 0 30px;
    transition: all 0.3s ease;

    &:hover {
      background: linear-gradient(45deg, #1976D2 30%, #1CB5E0 90%);
      transform: translateY(-2px);
      box-shadow: 0 6px 10px 2px rgba(33, 203, 243, .4);
    }
  }

  .MuiButton-outlined {
    border-radius: 8px;
    height: 48px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
  }

  // 信息展示区域
  .info-section {
    .MuiTypography-h6 {
      display: flex;
      align-items: center;
      margin-bottom: 1rem;
      color: #333;
      font-weight: 600;
    }

    .MuiSvgIcon-root {
      margin-right: 0.5rem;
      color: #2196F3;
    }
  }

  // 特性列表样式
  ul {
    list-style: none;
    padding-left: 0;

    li {
      position: relative;
      padding-left: 1.5rem;
      margin-bottom: 0.5rem;

      &::before {
        content: '✓';
        position: absolute;
        left: 0;
        color: #4CAF50;
        font-weight: bold;
      }
    }
  }

  // 空状态样式
  .empty-state {
    text-align: center;
    padding: 4rem 2rem;

    .MuiSvgIcon-root {
      font-size: 4rem;
      opacity: 0.3;
      margin-bottom: 1rem;
    }

    .MuiTypography-h6 {
      margin-bottom: 0.5rem;
      color: #666;
    }

    .MuiTypography-body2 {
      color: #999;
    }
  }

  // 使用说明卡片
  .usage-card {
    margin-top: 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;

    .MuiCardContent-root {
      padding: 2rem;
    }

    .MuiTypography-h6 {
      color: white;
      margin-bottom: 1.5rem;
    }

    .MuiTypography-subtitle2 {
      color: rgba(255, 255, 255, 0.9);
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    .MuiTypography-body2 {
      color: rgba(255, 255, 255, 0.8);
      line-height: 1.6;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .MuiContainer-root {
      padding-left: 1rem;
      padding-right: 1rem;
    }

    .MuiCard-root {
      margin-bottom: 1rem;
    }

    .MuiPaper-root {
      height: 60vh !important;
      min-height: 400px !important;
    }

    .empty-state {
      padding: 2rem 1rem;

      .MuiSvgIcon-root {
        font-size: 3rem;
      }
    }
  }

  @media (max-width: 480px) {
    .MuiTypography-h3 {
      font-size: 1.8rem;
    }

    .MuiTypography-h6 {
      font-size: 1rem;
    }

    .MuiButton-contained,
    .MuiButton-outlined {
      height: 40px;
      font-size: 0.875rem;
    }

    .usage-card .MuiCardContent-root {
      padding: 1rem;
    }
  }
}

// 拖拽上传效果
.pdf-demo-container {
  .upload-area {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      border-color: #2196F3;
      background-color: rgba(33, 150, 243, 0.05);
    }

    &.drag-over {
      border-color: #2196F3;
      background-color: rgba(33, 150, 243, 0.1);
      transform: scale(1.02);
    }
  }
}

// 加载动画
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.loading {
  animation: pulse 1.5s ease-in-out infinite;
}

// 成功状态动画
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-up {
  animation: slideInUp 0.5s ease-out;
}

// 错误状态样式
.MuiAlert-root {
  border-radius: 8px;
  margin-bottom: 1rem;

  &.MuiAlert-standardError {
    background-color: rgba(244, 67, 54, 0.1);
    border: 1px solid rgba(244, 67, 54, 0.2);
  }
}
