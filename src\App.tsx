import React, { Suspense } from "react";
import { useRout<PERSON>, <PERSON> } from "react-router-dom";
import routers from "./router";
import "@/assets/app.less";

import { useAppSelector, shallowEqualApp, useAppDispatch } from "./store";
import { incremented, decremented } from "./store/modules/counter";

const App: React.FC = () => {
  const { count } = useAppSelector(
    (state) => ({
      count: state.counter.count,
    }),
    shallowEqualApp
  );

  const dispatch = useAppDispatch();
  const addCount = () => dispatch(incremented());
  const subCount = () => dispatch(decremented());

  return (
    <div className="App">
      <div className="nav">
        <Link to="/">Home</Link>
        <Link to="/login">Login</Link>
      </div>
      <Suspense fallback="loading...">
        <div className="main">{useRoutes(routers as any)}</div>
        <div>count: {count}</div>
        <button onClick={subCount}>-1</button>
        <button onClick={addCount}>+1</button>
      </Suspense>
    </div>
  );
};

export default App;
