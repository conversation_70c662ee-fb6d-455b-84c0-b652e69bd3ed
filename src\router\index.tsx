import React, { lazy } from 'react';
import type { RouteObject } from 'react-router-dom';
import { Navigate } from 'react-router-dom';

const Home = lazy(() => import('@/views/Home/Home'));
const Login = lazy(() => import('@/views/Login/Login'));
const PDFDemo = lazy(() => import('@/views/PDFDemo/PDFDemoSimple'));

export interface RouteProps {
  path?: string;
  name?: string;
  index?: boolean;
  icon?: React.ReactNode;
  children?: React.ReactNode;
  caseSensitive?: boolean;
  id?: string;
  element?: React.ReactNode | null;
  Component?: React.ComponentType | null;
  errorElement?: React.ReactNode | null;
  ErrorBoundary?: React.ComponentType | null;
  handle?: RouteObject['handle'];
}

const routers: Array<RouteProps> = [
  {
    path: '/',
    element: <Navigate to="/home" />,
  },
  {
    path: '/home',
    name: 'home',
    element: <Home />,
  },
  {
    path: '/login',
    name: 'login',
    element: <Login />,
  },
  {
    path: '/pdf-demo',
    name: 'pdf-demo',
    element: <PDFDemo />,
  },
];

export default routers;

